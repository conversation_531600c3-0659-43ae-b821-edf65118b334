#!/usr/bin/env python3
"""
Test script for permissions.py functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from managers.permissions import get_permissions_by_role

def test_permissions():
    """Test permissions for different roles"""
    
    roles = ["manager", "service", "hygiene", "courier", "montage"]
    
    for role in roles:
        print(f"\n=== Testing role: {role} ===")
        permissions = get_permissions_by_role(role)
        
        if permissions is None:
            print(f"No permissions found for role: {role}")
            continue
            
        for action, config in permissions.items():
            print(f"{action}: allowed={config.get('allowed', False)}")
            if config.get('allowed') and 'sections' in config:
                sections = config['sections']
                print(f"  sections: {sections[:10]}{'...' if len(sections) > 10 else ''} (total: {len(sections)})")

if __name__ == "__main__":
    test_permissions()
