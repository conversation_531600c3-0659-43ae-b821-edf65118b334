"""
Permissions module for different operator roles.
Defines what actions and sections each role can access.
"""

from typing import Dict, List, Any, Optional
from os import getenv


permissions = {
    "manager": {
        "service_open":    {"allowed": True},
        "collection":      {"allowed": True},
        "product_insert":  {"allowed": True},
        "manager_mode":    {"allowed": True},
        "delivery":        {"allowed": True},
    },
    "service": {
        "service_open":    {"allowed": True},
        "collection":      {"allowed": True},
        "product_insert":  {"allowed": True},
        "manager_mode":    {"allowed": True},
        "delivery":        {"allowed": True},
    },
    "hygiene": {
        "service_open":    {"allowed": True},
        "collection":      {"allowed": False},
        "product_insert":  {"allowed": False},
        "manager_mode":    {"allowed": False},
        "delivery":        {"allowed": False},
    },
    "courier": {
        "service_open":    {"allowed": False},
        "collection":      {"allowed": True},
        "product_insert":  {"allowed": True},
        "manager_mode":    {"allowed": True},
        "delivery":        {"allowed": True},
    },
    "montage": {
        "service_open":    {"allowed": False},
        "collection":      {"allowed": True},
        "product_insert":  {"allowed": True},
        "manager_mode":    {"allowed": True},
        "delivery":        {"allowed": True},
    }
}


def get_hygiene_sections() -> List[int]:
    """
    Returns a list of sections available for hygiene.
    Reads comma separated string from HYGIENE_PERMITED_SECTIONS env variable.
    """
    hygiene_sections_str = getenv("HYGIENE_PERMITED_SECTIONS", "")
    
    if not hygiene_sections_str:
        return []
    
    return [int(section.strip()) for section in hygiene_sections_str.split(",")]

def update_hygiene():
    """Update hygiene permissions with available sections"""
    hygiene_sections = get_hygiene_sections()
    permissions["hygiene"]["collection"]["sections"] = hygiene_sections



def get_permissions_by_role(role: str) -> Optional[Dict[str, Any]]:

    match role:
        case "hygiene":
            update_hygiene()
    
    return permissions.get(role, None)
